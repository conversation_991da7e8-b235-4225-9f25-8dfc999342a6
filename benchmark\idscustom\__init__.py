r"""The usage of CIFAR100 is similar to CIFAR10. To create federated tasks based on this benchmark,
you can try the following example:

Example 1:
```python
>>> # create a IID partitioned CIFAR100 for 100 clients
>>> config = {'benchmark':{'name':'flgo.benchmark.cifar100_classification'},'partitioner':{'name': 'IIDPartitioner','para':{'num_clients':100}}}
>>> flgo.gen_task(config, task_path='./test_cifar100')
```
After running the above codes, there will be a task dictionary './test_mnist' that represents a federated task.
This task is corresponding to a static scenario where the samples in CIFAR100 is I.I.D. allocated to 100 clients,
and is also reusable for different algorithms so that a fair comparison can be easily achieved by optimizing
the same model on the same task.

Example 2:
```python
>>> # create a non-IID partitioned CIFAR100 for 100 clients
>>> niid_config = {'benchmark':{'name':'flgo.benchmark.cifar10_classification'},'partitioner':{'name': 'DirichletPartitioner','para':{'num_clients':100, 'alpha':0.5}}}
>>> flgo.gen_task(niid_config, task_path='./test_cifar_dir0.5')
```
To create a non-IID CIFAR100, the only difference is to specify different partitioners in configurations, where
the term `IIDPartitioner` is replaced by `DirichletPartitioner`. In addition, parameters of the partitioner can
be input by `para`.
"""
import flgo.benchmark.toolkits.visualization
import flgo.benchmark.toolkits.partition
import matplotlib.pyplot as plt
import matplotlib
import collections
import numpy as np
import os
import random
from flgoasyn.benchmark.idscustom.model import lt, lt2

# use_dp = os.environ.get('USE_DP', 'False')
# print(f"use_dp: {use_dp},  这是在lt 文件中=====================================================")

default_partitioner = flgo.benchmark.toolkits.partition.IIDPartitioner
default_partition_para = {'num_clients':20}
# default_model = lt
# if use_dp == 'True':
#     default_model = lt2
# else:
default_model = lt

# visualize = flgo.benchmark.toolkits.visualization.visualize_by_class

# 自定义的visualize_by_class函数
def visualize_by_class(generator, partitioner, task_path:str):
    """
    自定义的可视化函数，确保plt.ylim使用整数值，图像紧凑高质量，图例放在右下角。
    
    Args:
        generator (flgo.benchmark.toolkits.BasicTaskGenerator): 任务生成器
        partitioner (flgo.benchmark.toolkits.partition.BasicPartitioner): 分区器
        task_path (str): 存储图像的路径
    """
    all_labels = [d[-1] for d in generator.train_data]
    num_classes = len(set(all_labels))
    print(f"num_classes: {num_classes}")
    
    # 设置更紧凑的图形大小和更高的DPI
    plt.figure(figsize=(10, 8), dpi=300, tight_layout=True)
    
    colors = [key for key in matplotlib.colors.CSS4_COLORS.keys()]
    if len(colors) < num_classes: 
        colors = list(matplotlib.colors._colors_full_map.keys())
    random.shuffle(colors)
    
    # 为图例创建颜色映射
    color_map = {i: colors[i % len(colors)] for i in range(num_classes)}
    
    client_height = 0.8  # 减小高度使图像更紧凑
    
    if hasattr(generator.partitioner, 'num_parties'):
        n = generator.partitioner.num_parties
    else:
        n = generator.partitioner.num_clients
        
    if hasattr(partitioner, 'dirichlet_dist'):
        client_dist = generator.partitioner.dirichlet_dist.tolist()
        data_columns = [sum(cprop) for cprop in client_dist]
        # 确保客户端ID从0开始，并按数据量排序
        row_map = {k: i for k, i in zip(np.argsort(data_columns), range(n))}
        for cid, cprop in enumerate(client_dist):
            offset = 0
            y_bottom = row_map[cid] - client_height / 2.0
            y_top = row_map[cid] + client_height / 2.0
            for lbi in range(len(cprop)):
                plt.fill_between([offset, offset + cprop[lbi]], y_bottom, y_top, facecolor=colors[lbi])
                offset += cprop[lbi]
    else:
        data_columns = [len(cidx) for cidx in generator.local_datas]
        # 确保客户端ID从0开始，并按数据量排序
        row_map = {k: i for k, i in zip(np.argsort(data_columns), range(n))}
        for cid, cidxs in enumerate(generator.local_datas):
            labels = [int(generator.train_data[did][-1]) for did in cidxs]
            lb_counter = collections.Counter(labels)
            offset = 0
            y_bottom = row_map[cid] - client_height / 2.0
            y_top = row_map[cid] + client_height / 2.0
            for lbi in range(num_classes):
                plt.fill_between([offset, offset + lb_counter[lbi]], y_bottom, y_top, facecolor=colors[lbi % len(colors)])
                offset += lb_counter[lbi]
    
    # 设置坐标轴范围和标签            
    plt.xlim(0, max(data_columns) * 1.05)  # 增加一点右侧空间用于图例
    plt.ylim(-0.5, n-0.5)
    y_ticks = list(range(n))
    plt.yticks(y_ticks)
    plt.ylabel('Client ID', fontsize=16, fontweight='bold')
    plt.xlabel('Number of Samples', fontsize=16, fontweight='bold')
    
    # 调整坐标轴刻度
    plt.tick_params(axis='both', which='major', labelsize=14)
    
    # 添加网格线，增强可读性
    plt.grid(axis='y', linestyle='--', alpha=0.3)
    
    # 添加图例并放置在右下角
    legend_patches = []
    for i in range(num_classes):
        patch = matplotlib.patches.Patch(color=color_map[i], label=f'Class {i}')
        legend_patches.append(patch)
    
    # 根据类别数量调整图例位置和列数
    if num_classes > 10:
        ncol = 2  # 两列图例
    else:
        ncol = 1  # 一列图例
    
    # 将图例放在右下角，并调整大小
    plt.legend(handles=legend_patches, 
            #    title='Classes', 
               loc='lower right', 
               ncol=ncol, 
               fontsize=14,
            #    title_fontsize=13,
               framealpha=0.8,  # 半透明背景
               edgecolor='gray')  # 灰色边框
    
    # 保存高质量图像
    save_path = os.path.join(task_path, 'res.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    # # 保存矢量图供高质量打印或发表
    # svg_path = os.path.join(task_path, 'res.svg')
    # plt.savefig(svg_path, format='svg', bbox_inches='tight')
    
    plt.close()  # 关闭图形，释放资源
    
    # 打印图形生成成功的信息
    print(f"✅ 数据分布可视化图已成功生成并保存到: {os.path.abspath(save_path)}")
    # print(f"✅ 矢量图已保存到: {os.path.abspath(svg_path)}")

# 使用自定义的visualize_by_class函数
visualize = visualize_by_class