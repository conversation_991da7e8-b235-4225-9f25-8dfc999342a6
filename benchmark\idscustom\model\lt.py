import torch
import torch.nn.functional as F
from flgo.utils.fmodule import FModule
from flgoasyn.benchmark.idscustom.config import get_model
import os

class Model(FModule):
    """联邦学习框架中使用的Transformer入侵检测模型。

    封装了TransformerIDSModel，实现了FModule接口。
    """
    def __init__(self):
        super(Model, self).__init__()
        # 使用config.py中的get_model()函数获取模型实例
        self.model = get_model()
        if hasattr(self.model, 'compute_loss'):
            self.compute_loss = self.model.compute_loss

    def forward(self, data):
        """前向传播方法，处理输入数据并返回结果。

        Args:
            data: 输入数据，可以是以下格式之一：
                - 张量: 直接作为输入特征
                - 字典: 包含'x'键作为输入特征，可选'attention_mask'键
                - 元组/列表: 第一个元素为输入特征，第二个元素(如果存在)为标签
                
        Returns:
            模型输出的logits
        """
        # 处理输入数据，提取特征和attention_mask
        x, attention_mask = self._extract_inputs(data)
        
        # 调用TransformerIDSModel并返回logits
        output = self.model(x, attention_mask)
        return output['logits']

    def predict(self, data):
        """预测样本的类别。

        Args:
            data: 输入数据，可以是以下格式之一：
                - 张量: 直接作为输入特征
                - 字典: 包含'x'键作为输入特征，可选'attention_mask'键
                - 元组/列表: 第一个元素为输入特征，第二个元素(如果存在)为标签

        Returns:
            torch.Tensor: 预测的类别索引，形状为 [batch_size]
        """
                # 处理输入数据，提取特征和attention_mask
        x, attention_mask = self._extract_inputs(data)
        
        # 调用TransformerIDSModel的predict方法
        return self.model.predict(x, attention_mask)

        
    def _extract_inputs(self, data):
        """从不同格式的输入数据中提取特征和attention_mask。
        
        Args:
            data: 输入数据，可以是张量、字典或元组/列表
            
        Returns:
            tuple: (特征张量, attention_mask或None)
        """
                # 处理字典输入
        if isinstance(data, dict):
            x = data['x'] if 'x' in data else data.get('data', None)
            attention_mask = data.get('attention_mask', None)
            
            # 如果x为None但有其他可能的键
            if x is None:
                for key in data:
                    if isinstance(data[key], torch.Tensor) and key != 'attention_mask':
                        x = data[key]
                        break
                        
            if x is None:
                raise ValueError("无法从输入字典中提取特征数据")
                
        # 处理元组或列表输入（通常是dataloader的输出格式）
        elif isinstance(data, (tuple, list)):
            x = data[0]  # 第一个元素通常是特征
            attention_mask = None
            # 检查是否有额外的attention_mask输入
            if len(data) > 2 and isinstance(data[2], torch.Tensor):
                attention_mask = data[2]
                
        # 处理直接的张量输入
        elif isinstance(data, torch.Tensor):
            x = data
            attention_mask = None
            
        else:
            raise TypeError(f"不支持的输入类型: {type(data)}")
            
        return x, attention_mask



def init_local_module(object):
    """初始化本地模块。"""
    pass

def init_global_module(object):
    """初始化全局模块。"""
    if 'Server' in object.__class__.__name__:
            object.model = Model().to(object.device)