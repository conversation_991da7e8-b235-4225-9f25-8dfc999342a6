import os
import pickle
import datetime
import os.path as osp
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt


def load_metrics(index=None):
    """
    从all_metrics.pkl文件加载特定索引的metrics信息
    
    Args:
        index (int, optional): 要加载的metrics索引，如果不提供则加载最新的一条
            
    Returns:
        dict: 加载的metrics字典，如果文件不存在则返回None
    """
    # 加载所有metrics
    all_metrics = load_all_metrics()
    if not all_metrics:
        return None
    
    # 如果未指定索引，返回最新的一条
    if index is None:
        return all_metrics[-1]
    
    # 检查索引是否有效
    if index < 0 or index >= len(all_metrics):
        print(f"无效的索引: {index}，有效范围: 0-{len(all_metrics)-1}")
        return None
    
    return all_metrics[index]


def load_all_metrics():
    """
    加载所有metrics历史记录
    
    Returns:
        list: 包含所有metrics的列表，如果文件不存在则返回None
    """
    from flgoasyn.benchmark.idscustom.core import IDSCalculator
    
    # 确定metrics目录和默认文件
    metrics_dir = osp.join(os.getcwd(), 'metrics_results')
    default_file = osp.join(metrics_dir, IDSCalculator.DEFAULT_METRICS_FILE)
    
    if not osp.exists(metrics_dir) or not osp.exists(default_file):
        print(f"默认metrics文件不存在: {default_file}")
        return None
    
    try:
        with open(default_file, 'rb') as f:
            all_metrics = pickle.load(f)
        print(f"成功加载所有metrics，共{len(all_metrics)}条记录")
        return all_metrics
    except Exception as e:
        print(f"加载所有metrics文件时出错: {e}")
        return None


def list_metrics():
    """
    列出所有可用的metrics记录
    
    Returns:
        list: metrics列表
    """
    all_metrics = load_all_metrics()
    if not all_metrics:
        return []
    
    print(f"找到 {len(all_metrics)} 条metrics记录:")
    for i, metrics in enumerate(all_metrics):
        # 显示主要指标
        metrics_info = f"记录 {i}: "
        for key in ['accuracy', 'F1', 'precision', 'recall']:
            if key in metrics:
                metrics_info += f"{key}={metrics[key]:.4f} "
        print(metrics_info)
    
    return all_metrics


def plot_metrics_history(metrics_names=None, figsize=(12, 8), save_path=None):
    """
    绘制metrics历史趋势图
    
    Args:
        metrics_names (list, optional): 要绘制的metrics名称列表，默认为['accuracy', 'F1', 'precision', 'recall']
        figsize (tuple, optional): 图形大小
        save_path (str, optional): 保存图像的路径，如果不提供则显示图像
        
    Returns:
        matplotlib.figure.Figure: 生成的图形对象
    """
    if metrics_names is None:
        metrics_names = ['accuracy', 'F1', 'precision', 'recall']
    
    # 加载所有metrics
    all_metrics = load_all_metrics()
    if not all_metrics:
        print("没有可用的metrics数据")
        return None
    
    # 提取时间和指标值
    indices = list(range(len(all_metrics)))
    metrics_values = {name: [] for name in metrics_names}
    
    for m in all_metrics:
        for name in metrics_names:
            if name in m:
                metrics_values[name].append(m[name])
            else:
                metrics_values[name].append(None)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=figsize)
    
    for name in metrics_names:
        ax.plot(indices, metrics_values[name], 'o-', label=name)
    
    ax.set_xlabel('评估次数')
    ax.set_ylabel('指标值')
    ax.set_title('评估指标历史趋势')
    ax.legend()
    ax.grid(True)
    
    # 设置x轴刻度
    plt.xticks(indices)
    plt.tight_layout()
    
    # 保存或显示图形
    if save_path:
        plt.savefig(save_path)
        print(f"图形已保存到: {save_path}")
    else:
        plt.show()
    
    return fig


def compare_metrics(indices=None, metrics_names=None, figsize=(14, 10), save_path=None):
    """
    比较多个metrics记录的结果
    
    Args:
        indices (list, optional): 要比较的metrics索引列表，如果不提供则使用最新的5条记录
        metrics_names (list, optional): 要比较的metrics名称列表，默认为['accuracy', 'F1', 'precision', 'recall']
        figsize (tuple, optional): 图形大小
        save_path (str, optional): 保存图像的路径，如果不提供则显示图像
        
    Returns:
        pandas.DataFrame: 比较结果数据框
    """
    if metrics_names is None:
        metrics_names = ['accuracy', 'F1', 'precision', 'recall']
    
    # 加载所有metrics
    all_metrics = load_all_metrics()
    if not all_metrics:
        print("没有可用的metrics数据")
        return None
    
    # 如果未提供索引，使用最新的5条记录
    if indices is None:
        indices = list(range(max(0, len(all_metrics) - 5), len(all_metrics)))
    
    # 检查索引是否有效
    valid_indices = []
    for idx in indices:
        if 0 <= idx < len(all_metrics):
            valid_indices.append(idx)
        else:
            print(f"无效的索引: {idx}，已忽略")
    
    if not valid_indices:
        print("没有有效的索引")
        return None
    
    # 创建比较结果数据框
    data = []
    for idx in valid_indices:
        metrics = all_metrics[idx]
        row = {'index': idx}
        for name in metrics_names:
            if name in metrics:
                row[name] = metrics[name]
        data.append(row)
    
    df = pd.DataFrame(data)
    
    # 创建可视化
    fig, axes = plt.subplots(len(metrics_names), 1, figsize=figsize)
    if len(metrics_names) == 1:
        axes = [axes]
    
    for i, name in enumerate(metrics_names):
        if name in df.columns:
            axes[i].bar(df['index'], df[name])
            axes[i].set_title(f'{name} 比较')
            axes[i].set_ylabel(name)
            axes[i].set_xlabel('记录索引')
            
            # 在柱状图上添加数值标签
            for j, v in enumerate(df[name]):
                axes[i].text(df['index'].iloc[j], v, f'{v:.4f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存或显示图形
    if save_path:
        plt.savefig(save_path)
        print(f"比较图形已保存到: {save_path}")
    else:
        plt.show()
    
    return df


def analyze_confusion_matrix(index=None, class_names=None, normalize=True, figsize=(12, 10)):
    """
    分析混淆矩阵并生成详细的分类报告
    
    Args:
        index (int, optional): 要分析的metrics索引，如果不提供则使用最新的记录
        class_names (list, optional): 类别名称列表，如果不提供则使用数字索引
        normalize (bool, optional): 是否对混淆矩阵进行归一化，默认为True
        figsize (tuple, optional): 图形大小
        
    Returns:
        dict: 分析结果，包含混淆矩阵和分类报告
    """
    import seaborn as sns
    from sklearn.metrics import classification_report
    
    # 加载metrics数据
    metrics = load_metrics(index)
    if metrics is None:
        print("无法加载metrics数据")
        return None
    
    # 检查是否存在混淆矩阵和预测结果
    if 'confusion_matrix' not in metrics:
        if 'y_true' not in metrics or 'y_pred' not in metrics:
            print("metrics中缺少混淆矩阵或预测结果")
            return None
        
        # 如果没有混淆矩阵但有预测结果，计算混淆矩阵
        from sklearn.metrics import confusion_matrix
        y_true = np.array(metrics['y_true'])
        y_pred = np.array(metrics['y_pred'])
        cm = confusion_matrix(y_true, y_pred)
    else:
        cm = np.array(metrics['confusion_matrix'])
        
        # 如果需要原始预测结果
        if 'y_true' not in metrics or 'y_pred' not in metrics:
            print("警告: metrics中缺少原始预测结果，某些详细指标可能无法计算")
            y_true = None
            y_pred = None
        else:
            y_true = np.array(metrics['y_true'])
            y_pred = np.array(metrics['y_pred'])
    
    # 设置类别名称
    n_classes = cm.shape[0]
    if class_names is None:
        class_names = [str(i) for i in range(n_classes)]
    elif len(class_names) != n_classes:
        print(f"警告: 提供的类别名称数量 ({len(class_names)}) 与混淆矩阵大小 ({n_classes}) 不匹配")
        class_names = [str(i) for i in range(n_classes)]
    
    # 归一化混淆矩阵
    if normalize:
        cm_norm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        cm_display = cm_norm
        fmt = '.2f'
    else:
        cm_display = cm
        fmt = 'd'
    
    # 创建图形
    plt.figure(figsize=figsize)
    sns.heatmap(cm_display, annot=True, fmt=fmt, cmap='Blues',
               xticklabels=class_names, yticklabels=class_names)
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    plt.title('混淆矩阵' + (' (归一化)' if normalize else ''))
    plt.tight_layout()
    plt.show()
    
    # 如果有原始预测结果，生成分类报告
    if y_true is not None and y_pred is not None:
        report = classification_report(y_true, y_pred, target_names=class_names, zero_division=0)
        print("分类报告:")
        print(report)
    else:
        report = None
    
    # 返回分析结果
    return {
        'confusion_matrix': cm,
        'normalized_confusion_matrix': cm_norm if normalize else None,
        'class_names': class_names,
        'classification_report': report
    }


def export_to_csv(index=None, output_path=None, exclude_fields=None):
    """
    将metrics导出为CSV格式
    
    Args:
        index (int, optional): 要导出的metrics索引，如果不提供则使用最新的记录
        output_path (str, optional): 输出CSV文件的路径，如果不提供则使用默认路径
        exclude_fields (list, optional): 要排除的字段列表，例如['y_true', 'y_pred', 'confusion_matrix']
        
    Returns:
        str: 导出的CSV文件路径
    """
    # 设置默认排除字段
    if exclude_fields is None:
        exclude_fields = ['y_true', 'y_pred', 'confusion_matrix']
    
    # 加载metrics数据
    metrics = load_metrics(index)
    if metrics is None:
        print("无法加载metrics数据")
        return None
    
    # 设置默认输出路径
    if output_path is None:
        metrics_dir = osp.join(os.getcwd(), 'metrics_results')
        os.makedirs(metrics_dir, exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = osp.join(metrics_dir, f"metrics_export_{timestamp}.csv")
    
    # 创建一个新的字典，排除指定的字段
    filtered_metrics = {k: v for k, v in metrics.items() if k not in exclude_fields}
    
    # 处理嵌套结构或复杂对象
    for k, v in filtered_metrics.items():
        if isinstance(v, (list, dict)):
            filtered_metrics[k] = str(v)  # 将复杂对象转换为字符串
    
    # 创建数据框并导出为CSV
    df = pd.DataFrame([filtered_metrics])
    df.to_csv(output_path, index=False)
    print(f"Metrics已导出到CSV文件: {output_path}")
    
    return output_path


if __name__ == "__main__":
    # 示例用法
    print("Metrics Reader 工具")
    print("可用功能:")
    print("1. 列出所有metrics记录")
    print("2. 加载特定索引的metrics")
    print("3. 加载所有历史metrics")
    print("4. 绘制metrics历史趋势")
    print("5. 比较多个metrics记录")
    print("6. 分析混淆矩阵")
    print("7. 导出metrics到CSV")
    
    choice = input("请选择功能 (1-7): ")
    
    if choice == '1':
        list_metrics()
    elif choice == '2':
        idx = input("请输入要加载的metrics索引 (留空则加载最新的): ")
        idx = int(idx) if idx.strip() else None
        metrics = load_metrics(idx)
        if metrics:
            print("成功加载metrics:")
            for k, v in metrics.items():
                if k not in ['y_true', 'y_pred', 'confusion_matrix']:
                    print(f"  {k}: {v}")
    elif choice == '3':
        load_all_metrics()
    elif choice == '4':
        plot_metrics_history()
    elif choice == '5':
        indices_input = input("请输入要比较的metrics索引，用逗号分隔 (留空则比较最新的5条): ")
        indices = [int(idx.strip()) for idx in indices_input.split(',')] if indices_input.strip() else None
        compare_metrics(indices)
    elif choice == '6':
        idx = input("请输入要分析的metrics索引 (留空则分析最新的): ")
        idx = int(idx) if idx.strip() else None
        analyze_confusion_matrix(idx)
    elif choice == '7':
        idx = input("请输入要导出的metrics索引 (留空则导出最新的): ")
        idx = int(idx) if idx.strip() else None
        export_to_csv(idx)
    else:
        print("无效的选择") 